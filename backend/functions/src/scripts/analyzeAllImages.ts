#!/usr/bin/env ts-node

import * as fs from "fs";
import * as path from "path";
import OpenAI from "openai";
import { PigeonDistinctiveness } from "../domain/enums/PigeonDistinctiveness";
import { BirdType } from "../domain/enums/BirdType";

// Load environment variables
import "../env";

// Type for the OpenAI analysis response
interface PigeonAnalysisResult {
    filename: string;
    looks_like_a_screenshot: number; // 1 to 10
    is_bird: boolean;
    bird_type: string; // "pigeon" | "crow" | "seagull" | "sparrow" | "magpie"
    is_baby_pigeon: boolean;
    is_dead: boolean;
    distinctiveness: string; // "COMMON", "UNUSUAL", "RARE"
    description: string; // Short description (max 80 words) of the pigeon's plumage and patterns
    error?: string; // In case of processing error
}

class ImageAnalyzer {
    private openAIClient: OpenAI;
    private tmpDir: string;
    private resultsDir: string;
    private results: PigeonAnalysisResult[] = [];

    constructor() {
        this.openAIClient = new OpenAI();
        this.tmpDir = path.join(__dirname, "../tmp");
        this.resultsDir = path.join(__dirname, "../tmp/results");
    }

    /**
     * Get all image files from the tmp directory
     */
    private getImageFiles(): string[] {
        const files = fs.readdirSync(this.tmpDir);
        return files.filter((file) => {
            const ext = path.extname(file).toLowerCase();
            return [".jpg", ".jpeg", ".png", ".gif", ".webp"].includes(ext);
        });
    }

    /**
     * Convert local image file to base64 data URL for OpenAI
     */
    private imageToDataUrl(filePath: string): string {
        const imageBuffer = fs.readFileSync(filePath);
        const ext = path.extname(filePath).toLowerCase();
        let mimeType = "image/jpeg";

        switch (ext) {
            case ".png":
                mimeType = "image/png";
                break;
            case ".gif":
                mimeType = "image/gif";
                break;
            case ".webp":
                mimeType = "image/webp";
                break;
        }

        return `data:${mimeType};base64,${imageBuffer.toString("base64")}`;
    }

    /**
     * Analyze a single image using OpenAI
     */
    private async analyzeImage(filename: string): Promise<PigeonAnalysisResult> {
        console.log(`Analyzing ${filename}...`);

        try {
            const filePath = path.join(this.tmpDir, filename);
            const imageDataUrl = this.imageToDataUrl(filePath);

            //
            // 1) Structured Outputs JSON Schema (as expected by OpenAI)
            //    Docs: https://platform.openai.com/docs/guides/structured-outputs
            //
            const BirdPhenotypeSchema = {
                name: "BirdPhenotype",
                strict: true,
                schema: {
                    type: "object",
                    additionalProperties: false,
                    properties: {
                        looks_like_a_screenshot: { type: "integer", minimum: 1, maximum: 10 },
                        image_quality: { type: "integer", minimum: 1, maximum: 10 },
                        is_bird: { type: "boolean" },

                        species: {
                            type: "string",
                            enum: [
                                "columba_livia",
                                "turtledove",
                                "wood_pigeon",
                                "dove",
                                "sparrow",
                                "seagull",
                                "gull",
                                "crow",
                                "raven",
                                "starling",
                                "hen",
                                "rooster",
                                "duck",
                                "magpie",
                                "other",
                            ],
                        },

                        is_baby_pigeon: { type: "boolean" },
                        is_dead: { type: "boolean" },
                        distinctiveness: { type: "string", enum: ["common", "unusual", "rare"] },

                        description: {
                            type: "string",
                            maxLength: 350, // ≤50 words; character cap is safer for JSON Schema
                        },

                        confidence: { type: "integer", minimum: 1, maximum: 10 },

                        behavior: { type: ["string", "null"] },
                        context: { type: ["string", "null"] },

                        // Pigeon-specific traits: object or null (null when species != "columba_livia")
                        pigeon_traits: {
                            type: ["object", "null"],
                            additionalProperties: false,
                            properties: {
                                base_color: {
                                    type: "string",
                                    enum: ["blue", "ash-red", "brown", "other"],
                                },
                                main_pattern: {
                                    type: "string",
                                    enum: ["bar", "checker", "t-check", "barless", "spread", "solid", "other"],
                                },
                                is_spread: { type: "boolean" },
                                spread_level: {
                                    type: "string",
                                    enum: ["none", "partial", "full"],
                                },
                                is_dilute: { type: "boolean" },
                                dilute_level: {
                                    type: "string",
                                    enum: ["none", "light", "full"],
                                },

                                is_piebald: { type: "boolean" },
                                piebald_level: {
                                    type: "string",
                                    enum: ["none", "light", "intermediate", "heavy", "full_white"],
                                },

                                piebald_distribution: {
                                    type: ["object", "null"],
                                    additionalProperties: false,
                                    properties: {
                                        head: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                        neck: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                        back: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                        wings: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                        tail: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                        belly: {
                                            type: "string",
                                            enum: ["none", "light", "intermediate", "heavy", "full"],
                                        },
                                    },
                                    required: ["head", "neck", "back", "wings", "tail", "belly"],
                                },

                                head_pattern: {
                                    type: "string",
                                    enum: ["helmet", "baldhead", "none"],
                                },
                                neck_pattern: {
                                    type: "string",
                                    enum: ["white_collar", "none"],
                                },
                                body_white_pattern: {
                                    type: "string",
                                    enum: ["saddle", "shield", "rump", "vent", "mottled", "none", "other"],
                                },
                                has_grizzle: { type: "boolean" },
                                has_recessive_red: { type: "boolean" },

                                visible_color: {
                                    type: "string",
                                    enum: ["blue", "black", "brown", "ash-red", "silver", "white", "mixed", "other"],
                                },
                                iridescence_level: {
                                    type: "string",
                                    enum: ["none", "low", "medium", "high"],
                                },
                                tail_color: {
                                    type: "string",
                                    enum: ["normal", "white", "black", "mixed"],
                                },
                                wing_tip_color: {
                                    type: "string",
                                    enum: ["black", "dark", "grey", "light", "white"],
                                },
                                special_face_mark: { type: ["string", "null"] },
                                face_pattern: {
                                    type: "string",
                                    enum: ["uniform_standard", "mottled", "weird_spots"],
                                },
                                dirtiness_level: {
                                    type: "string",
                                    enum: ["clean", "average", "dirty"],
                                },
                                body_size: {
                                    type: "string",
                                    enum: ["slim", "average", "plump", "very_plump"],
                                },
                                has_broken_leg: { type: "boolean" },
                                has_banded_leg: { type: "boolean" },
                                other_notable_trait: { type: ["string", "null"] },
                            },
                            required: [
                                "base_color",
                                "main_pattern",
                                "is_spread",
                                "spread_level",
                                "is_dilute",
                                "dilute_level",
                                "is_piebald",
                                "piebald_level",
                                "piebald_distribution",
                                "head_pattern",
                                "neck_pattern",
                                "body_white_pattern",
                                "has_grizzle",
                                "has_recessive_red",
                                "visible_color",
                                "iridescence_level",
                                "tail_color",
                                "special_face_mark",
                                "face_pattern",
                                "wing_tip_color",
                                "dirtiness_level",
                                "body_size",
                                "has_broken_leg",
                                "has_banded_leg",
                                "other_notable_trait",
                            ],
                        },
                    },
                    required: [
                        "looks_like_a_screenshot",
                        "image_quality",
                        "is_bird",
                        "species",
                        "is_baby_pigeon",
                        "is_dead",
                        "distinctiveness",
                        "description",
                        "confidence",
                        "behavior",
                        "context",
                        "pigeon_traits",
                    ],
                },
            } as const;

            //
            // 2) System prompt crafted from your “Persona” + “Task”
            //
            const SYSTEM_PROMPT = `
You are a bird phenotype analysis expert, trained to identify and classify birds from photos.
You specialize in urban birds, especially feral pigeons (Columba livia / pigeon biset), and can detect fine-grained visual traits related to genetics, plumage, and morphology.

Task:
- Analyze the image and return a valid JSON object describing the central bird.
- If multiple birds: analyze only the one closest to the center.
- If no bird is visible: return "is_bird": false.
- Use null for any uncertain value.
- Do not describe: posture, attitude, orientation, lighting, or camera angle.
- Follow the field definitions strictly.

Important:
- If species != "columba_livia", set "pigeon_traits": null.
- If species == "columba_livia", fill "pigeon_traits" according to the schema and return null for unknown subfields.
`.trim();

            const resp = await this.openAIClient.responses.create({
                model: "gpt-4.1-mini", // any vision-capable model that supports structured outputs
                input: [
                    {
                        role: "system",
                        content: SYSTEM_PROMPT,
                    },
                    {
                        role: "user",
                        content: [
                            {
                                type: "input_text",
                                text: "Analyze this image and return the BirdPhenotype JSON.",
                            },
                            {
                                type: "input_image",
                                detail: "low",
                                image_url: imageDataUrl,
                            },
                        ],
                    },
                ],
                text: {
                    format: {
                        type: "json_schema",
                        name: BirdPhenotypeSchema.name,
                        schema: BirdPhenotypeSchema.schema,
                        strict: BirdPhenotypeSchema.strict,
                    },
                },
            });
            if (resp.error) {
                throw new Error(`OpenAI error: ${resp.error.message}`);
            }
            if (!resp.output_text) {
                throw new Error("No response from OpenAI for pigeon analysis");
            }

            // The SDK returns JSON already parsed when using structured outputs
            const parsedResult = JSON.parse(resp.output_text)
            return {
                filename,
                ...parsedResult,
            };
        } catch (error) {
            console.error(`Error analyzing ${filename}:`, error);
            return {
                filename,
                looks_like_a_screenshot: 0,
                is_bird: false,
                bird_type: BirdType.PIGEON,
                is_baby_pigeon: false,
                is_dead: false,
                distinctiveness: PigeonDistinctiveness.COMMON,
                description: "",
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    /**
     * Process all images in the tmp directory
     */
    async processAllImages(): Promise<void> {
        const imageFiles = this.getImageFiles();
        console.log(`Found ${imageFiles.length} image files to process`);

        // Process images with a delay to avoid rate limiting
        for (let i = 0; i < imageFiles.length; i++) {
            const filename = imageFiles[i];
            console.log(`Processing ${i + 1}/${imageFiles.length}: ${filename}`);

            const result = await this.analyzeImage(filename);
            this.results.push(result);

            // Add a small delay between requests to avoid rate limiting
            if (i < imageFiles.length - 1) {
                await new Promise((resolve) => setTimeout(resolve, 1000));
            }
        }
    }

    /**
     * Save results to JSON file
     */
    saveResults(): void {
        const outputPath = path.join(__dirname, "../tmp/analysis_results.json");
        fs.writeFileSync(outputPath, JSON.stringify(this.results, null, 2));
        console.log(`Results saved to ${outputPath}`);

        // Also save a summary
        const summary = {
            total_images: this.results.length,
            successful_analyses: this.results.filter((r) => !r.error).length,
            failed_analyses: this.results.filter((r) => r.error).length,
            birds_detected: this.results.filter((r) => r.is_bird).length,
            pigeons: this.results.filter((r) => r.bird_type === BirdType.PIGEON).length,
            distinctiveness_breakdown: {
                common: this.results.filter((r) => r.distinctiveness === PigeonDistinctiveness.COMMON).length,
                unusual: this.results.filter((r) => r.distinctiveness === PigeonDistinctiveness.UNUSUAL).length,
                rare: this.results.filter((r) => r.distinctiveness === PigeonDistinctiveness.RARE).length,
            },
        };

        const summaryPath = path.join(__dirname, "../tmp/analysis_summary.json");
        fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
        console.log(`Summary saved to ${summaryPath}`);
    }
}

// Main execution
async function main() {
    console.log("Starting image analysis script...");

    const analyzer = new ImageAnalyzer();

    try {
        await analyzer.processAllImages();
        analyzer.saveResults();
        console.log("Analysis completed successfully!");
    } catch (error) {
        console.error("Script failed:", error);
        process.exit(1);
    }
}

// Run the script if called directly
if (require.main === module) {
    main();
}
